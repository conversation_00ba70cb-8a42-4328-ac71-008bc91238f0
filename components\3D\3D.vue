<template>
	<web-view :src="url" v-if="url!=''" />
</template>

<script setup>
import { callAiPolling } from "@/Api/index.js"
import { onMounted, onBeforeUnmount ,ref} from "vue"
const emit = defineEmits(['update:loading', 'update:percent']);

let timer = null

onMounted(() => {



	emit('update:loading', true)
	let operationId = uni.getStorageSync('operationId')
	if (operationId) {
		getStatus(operationId)

		timer = setInterval(async () => {
			getStatus(operationId)
		}, 3000)
	}
})
onBeforeUnmount(() => {

	clearInterval(timer)
})
async function getStatus(operationId) {
	let { data } = await callAiPolling({ operationId, type: 'model' })

	console.log(data);
	const completedCount = data.data.filter(item => item.aiStatus == '1').length
	const totalCount = data.data.length
	const percent = Math.round((completedCount / totalCount) * 100)

	// 更新进度
	emit('update:percent', percent)

	let status = data.data.every(item => item.aiStatus == '1')
	if (status) {
		clearInterval(timer)
		// 完成后隐藏loading
		emit('update:loading', false)
		emit('update:percent', 100)
    organizeData(data.data)
		
	}

}
let model = ref({})
let url = ref('')
function organizeData(data){

	model.value = JSON.parse(data.filter(item=>item.aiType=="model")[0].aiResult)
	console.log(model.value);
	url.value = `https://4tellai.com/AisimulatePreviewByObj?obj=${model.value.mesh_model}&mtl=${model.value.mesh_mtl}&jpg=${model.value.mesh_jpg}`
	console.log(url.value);
	
}
</script>

<style lang="scss"></style>