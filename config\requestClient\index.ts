// config/requestClient/index.ts
import {
	decryptBase64,
	decryptWithAes,
	encryptBase64,
	encryptWithAes,
	generateAesKey,
} from './encryption/crypto';
import * as encryptUtil from './encryption/jsencrypt';
import { useAppConfig } from './use-app-config'

const { clientId } : any = useAppConfig();

const useappConfig = {
	enableEncrypt: true,
	apiURL: process.env.NODE_ENV === 'production' ? '/' : '',
	clientId: 'e5cd7e4891bf95d1d19206ce24a7b32e'
}

export const encryptedFetch = async (method = 'POST', path : any, data : any) => {
	const headers = new Headers({
		'Content-Type': 'application/json',
		'clientid': clientId,
	});
	data.clientId = 'e5cd7e4891bf95d1d19206ce24a7b32e';
	data.grantType = 'password';
	// 请求加密处理
	if (useappConfig.enableEncrypt && ['POST', 'PUT'].includes(method)) {
		const aesKey = generateAesKey();
		headers.set('encrypt-key', encryptUtil.encrypt(encryptBase64(aesKey)));

		data = typeof data === 'object'
			? encryptWithAes(JSON.stringify(data), aesKey)
			: encryptWithAes(data, aesKey);
	}

	const response = await fetch(`${useappConfig.apiURL}${path}`, {
		method,
		headers,
		body: data ? JSON.stringify(data) : undefined
	});

	// 响应解密处理
	const encryptKey = response.headers.get('encrypt-key');
	if (encryptKey) {
		const base64Str : any = encryptUtil.decrypt(encryptKey);
		const aesSecret = decryptBase64(base64Str);
		const decrypted = decryptWithAes(await response.text(), aesSecret);
		return JSON.parse(decrypted);
	}

	return response.json();
}