<template>
  <view class="camera-container">
    <!-- H5平台使用video元素 -->
    <!-- #ifdef H5 -->
    <video v-if="isBrowser" ref="videoElement" id="cameraPreview" autoplay playsinline class="camera-view"></video>
    <!-- #endif -->

    <!-- 非H5平台使用camera组件 -->
    <!-- #ifndef H5 -->
    <camera v-if="!isBrowser" ref="camera" :device-position="devicePosition" flash="off" class="camera-view"
      @error="cameraError"></camera>
    <!-- #endif -->

    <!-- 顶部提示 -->
    <view class="top-overlay">
      <view class="flex items-center">
        <uni-icons type="info" size="20" color="#fff" class="mr-2"></uni-icons>
        <text class="text-center text-lg font-medium text-white">请将头发扎起，露出完整面部</text>
      </view>
      <text class="mt-1 text-center text-sm text-white opacity-80">确保光线充足，面部清晰可见</text>
    </view>

    <!-- 取景框区域 -->
    <view class="viewfinder-container">
      <view class="viewfinder-oval">
        <image src="/static/face-outline.png" class="face-outline" mode="aspectFit"></image>
      </view>

      <view class="status-indicator">
        <uni-icons :type="isFaceInFrame ? 'checkmarkempty' : 'info'" :color="isFaceInFrame ? '#00ff00' : '#ff0000'"
          size="20" class="mr-2"></uni-icons>
        <text class="text-sm font-medium">{{ isFaceInFrame ? '面部位置正确' : '请将面部置于取景框内' }}</text>
      </view>

      <view class="viewfinder-hint">
        <text>请保持面部在取景框内，并露出完整轮廓</text>
      </view>
    </view>

    <!-- 底部控制区 -->
    <view class="bottom-overlay">
      <view class="controls">
        <button class="control-btn" @tap="closeCamera">
          <uni-icons type="arrowleft" size="24" color="#fff"></uni-icons>
        </button>

        <button class="capture-btn" :class="{ 'disabled': !isFaceInFrame }" @tap="takePhoto" :disabled="!isFaceInFrame">
          <view class="capture-inner"></view>
        </button>

        <button class="control-btn" @tap="switchCamera">
          <uni-icons type="camera" size="24" color="#fff"></uni-icons>
        </button>
      </view>
    </view>

    <!-- 照片预览 -->
    <view v-if="capturedImage" class="preview-modal">
      <image :src="capturedImage" class="preview-image" mode="widthFix"></image>
      <view class="preview-actions">
        <button class="preview-btn retake" @tap="retakePhoto">重拍</button>
        <button class="preview-btn upload" @tap="savePhoto">上传</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// 响应式数据
const videoElement = ref(null);
const camera = ref(null);
const isFaceInFrame = ref(false);
const capturedImage = ref(null);
const devicePosition = ref('front');
const isFlashing = ref(false);
const isBrowser = ref(false);
const stream = ref(null);
const error = ref('');

// 检测运行环境
const detectEnvironment = () => {
  // #ifdef H5
  isBrowser.value = true;
  // #endif
};

// 初始化摄像头
const initCamera = async () => {
  try {
    // H5 平台使用 Web API
    if (isBrowser.value) {
      await initBrowserCamera();
    } else {
      // 小程序和 App 平台使用原生 camera 组件
      await initNativeCamera();
    }
  } catch (error) {
    handleCameraError(error);
  }
};

// 初始化浏览器摄像头
const initBrowserCamera = async () => {
  try {
    if (!navigator.mediaDevices?.getUserMedia) {
      throw new Error('当前浏览器不支持摄像头访问');
    }

    const constraints = {
      video: {
        facingMode: devicePosition.value === 'front' ? 'user' : 'environment',
      }
    };

    const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
    stream.value = mediaStream;

    // 确保video元素存在
    if (!videoElement.value) {
      throw new Error('视频元素未找到');
    }

    videoElement.value.srcObject = mediaStream;

    // 应用镜像效果
    if (devicePosition.value === 'front') {
      // videoElement.value.style.transform = 'scaleX(-1)';
    }

    // 等待视频加载完成
    await new Promise((resolve) => {
      videoElement.value.onloadedmetadata = resolve;
    });

    // 尝试播放视频
    try {
      await videoElement.value.play();
    } catch (playError) {
      console.warn('自动播放失败:', playError);
      videoElement.value.muted = true;
      await videoElement.value.play();
    }

    // 重置错误状态
    error.value = '';

  } catch (err) {
    throw err;
  }
};

// 初始化原生摄像头
const initNativeCamera = () => {
  return new Promise((resolve) => {
    // 小程序和App平台不需要额外初始化
    // camera组件会自动启动
    setTimeout(resolve, 500);
  });
};

// 拍照
const takePhoto = () => {
  if (!isFaceInFrame.value) return;

  if (isBrowser.value) {
    takePhotoBrowser();
  } else {
    takePhotoNative();
  }
};

// 浏览器端拍照
const takePhotoBrowser = () => {
  const video = videoElement.value;
  if (!video) return;

  const canvas = document.createElement('canvas');
  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;
  const ctx = canvas.getContext('2d');

  // 绘制当前视频帧
  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

  // 应用镜像效果
  if (devicePosition.value === 'front') {
    ctx.scale(-1, 1);
    ctx.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);
  }

  // 转换为图片
  showFlashEffect();
  capturedImage.value = canvas.toDataURL('image/jpeg');
};

// 原生平台拍照
const takePhotoNative = () => {
  const ctx = uni.createCameraContext();
  ctx.takePhoto({
    quality: 'high',
    success: (res) => {
      showFlashEffect();
      capturedImage.value = res.tempImagePath;
    },
    fail: (err) => {
      uni.showToast({
        title: '拍照失败',
        icon: 'none'
      });
    }
  });
};

// 显示闪光效果
const showFlashEffect = () => {
  isFlashing.value = true;
  setTimeout(() => {
    isFlashing.value = false;
  }, 300);
};

// 切换摄像头
const switchCamera = () => {
  devicePosition.value = devicePosition.value === 'front' ? 'back' : 'front';

  if (isBrowser.value) {
    closeCamera();
    initBrowserCamera();
  }
};

// 关闭摄像头
const closeCamera = () => {
  if (stream.value) {
    stream.value.getTracks().forEach(track => track.stop());
    stream.value = null;
  }
};

// 关闭页面
const closePage = () => {
  closeCamera();
  uni.navigateBack();
};

// 重新拍照
const retakePhoto = () => {
  capturedImage.value = null;
};

// 保存照片
const savePhoto = async () => {
  if (!capturedImage.value) return;

  uni.showLoading({
    title: '上传中...'
  });

  // 这里调用上传API
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '上传成功',
      icon: 'success'
    });

    // 上传成功后，返回结果
    uni.$emit('photoUploaded', capturedImage.value);
    uni.navigateBack();
  }, 1500);
};

// 摄像头错误处理
const cameraError = (e) => {
  console.error('摄像头错误:', e);
  handleCameraError(e.detail);
};

// 处理摄像头错误
const handleCameraError = (error) => {
  console.error('摄像头错误:', error);

  let errorMsg = '摄像头初始化失败';

  if (error.name === 'NotReadableError' && error.message.includes('Device in use')) {
    errorMsg = '摄像头设备被占用，请关闭其他使用摄像头的应用';
  } else if (error.name === 'OverconstrainedError') {
    errorMsg = '无法满足摄像头配置要求';
  } else if (error.name === 'NotFoundError') {
    errorMsg = '未找到可用的摄像头设备';
  } else if (error.name === 'NotAllowedError') {
    errorMsg = '用户拒绝了摄像头访问权限';
  } else if (error.name === 'SecurityError') {
    errorMsg = '安全限制阻止了摄像头访问';
  }

  uni.showToast({
    title: errorMsg,
    icon: 'none'
  });
};

// 页面加载
onLoad(() => {
  detectEnvironment();
  initCamera();
});

// 页面卸载
onBeforeUnmount(() => {
  closeCamera();
});
</script>

<style lang="scss">
.camera-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 1000;
}

.camera-view {
  width: 100%;
  height: 100%;
}

.top-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: var(--status-bar-height);
  z-index: 10;
}

.viewfinder-container {
  position: absolute;
  top: 100px;
  bottom: 150px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.viewfinder-oval {
  width: 300rpx;
  height: 400rpx;
  border: 2px dashed rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.face-outline {
  width: 220rpx;
  height: 300rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.4;
}

.status-indicator {
  position: absolute;
  top: 20rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}

.viewfinder-hint {
  position: absolute;
  bottom: 20rpx;
  left: 0;
  right: 0;
  text-align: center;
  color: #fff;
  font-size: 14px;
}

.bottom-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150px;
  background: rgba(0, 0, 0, 0.5);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10;
}

.controls {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
}

.control-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.capture-btn {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid #fff;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;

  &.disabled {
    opacity: 0.5;
  }

  .capture-inner {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: #fff;
  }
}

.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.preview-image {
  width: 80%;
  max-height: 70vh;
  margin-bottom: 20px;
}

.preview-actions {
  display: flex;
  gap: 20px;
}

.preview-btn {
  padding: 10px 25px;
  border-radius: 25px;
  color: #fff;
  font-size: 16px;

  &.retake {
    background: #f44336;
  }

  &.upload {
    background: #4CAF50;
  }
}

.flash-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  opacity: 0;
  animation: flash 0.3s ease-out;
}

@keyframes flash {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 0.9;
  }

  100% {
    opacity: 0;
  }
}

/* 小程序平台隐藏 video 元素 */
/* #ifndef H5 */
video {
  display: none;
}

/* #endif */
</style>
