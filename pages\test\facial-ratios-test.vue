<template>
  <view class="test-container">
    <view class="header">
      <text class="title">面部比例绘制测试</text>
    </view>
    
    <view class="content">
      <button @click="testFacialRatios" class="test-btn">测试面部比例绘制</button>
      
      <view v-if="testResult" class="result">
        <text class="result-title">测试结果:</text>
        <text class="result-text">{{ testResult }}</text>
      </view>
      
      <!-- 测试用的图片和canvas -->
      <view class="image-container" v-if="showTest">
        <image 
          class="test-image" 
          src="/static/test-face.jpg" 
          mode="widthFix"
          @load="onTestImageLoad"
        />
        <canvas 
          v-if="showCanvas" 
          canvas-id="testCanvas" 
          id="testCanvas" 
          :style="canvasStyle"
          style="position: absolute; top: 0; left: 0; pointer-events: none;"
        ></canvas>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, nextTick } from 'vue';

const testResult = ref('');
const showTest = ref(false);
const showCanvas = ref(false);
const canvasStyle = ref({});

// 模拟的facial_ratios数据
const mockFacialRatios = {
  "eye_width_percent": 71.73,
  "facial_index": 0.8,
  "forehead_ratio": 1.93,
  "lip_width_percent": 28.37,
  "midface_ratio": 0.78,
  "nose_width_percent": 10.4
};

// 模拟的landmarks数据
const mockLandmarks = {
  "left_eye": [
    {"x": 603, "y": 667},
    {"x": 626, "y": 649},
    {"x": 653, "y": 649},
    {"x": 675, "y": 664},
    {"x": 655, "y": 673},
    {"x": 628, "y": 673}
  ],
  "right_eye": [
    {"x": 406, "y": 662},
    {"x": 431, "y": 647},
    {"x": 459, "y": 650},
    {"x": 481, "y": 668},
    {"x": 457, "y": 671},
    {"x": 428, "y": 671}
  ],
  "left_eyebrow": [
    {"x": 574, "y": 596},
    {"x": 612, "y": 580},
    {"x": 653, "y": 580},
    {"x": 690, "y": 592},
    {"x": 718, "y": 618}
  ],
  "right_eyebrow": [
    {"x": 355, "y": 618},
    {"x": 386, "y": 589},
    {"x": 428, "y": 579},
    {"x": 470, "y": 584},
    {"x": 508, "y": 602}
  ],
  "nose_bridge": [
    {"x": 543, "y": 656},
    {"x": 544, "y": 693},
    {"x": 544, "y": 729},
    {"x": 545, "y": 767}
  ],
  "nose_tip": [
    {"x": 505, "y": 807},
    {"x": 524, "y": 812},
    {"x": 543, "y": 816},
    {"x": 563, "y": 812},
    {"x": 582, "y": 808}
  ],
  "outer_lips": [
    {"x": 464, "y": 892},
    {"x": 494, "y": 870},
    {"x": 524, "y": 858},
    {"x": 544, "y": 864},
    {"x": 564, "y": 857},
    {"x": 593, "y": 869},
    {"x": 621, "y": 891},
    {"x": 593, "y": 909},
    {"x": 565, "y": 920},
    {"x": 543, "y": 923},
    {"x": 520, "y": 921},
    {"x": 493, "y": 913}
  ]
};

const testFacialRatios = () => {
  testResult.value = '开始测试面部比例绘制功能...';
  showTest.value = true;
  
  // 模拟存储detectInfo数据
  const mockDetectInfo = {
    detect: JSON.stringify({
      faces: [{
        facial_ratios: mockFacialRatios,
        landmarks: Object.keys(mockLandmarks).flatMap(part => 
          mockLandmarks[part].map((point, index) => ({
            ...point,
            part: part,
            id: index
          }))
        )
      }]
    }),
    imgWidth: 1080,
    imgHeight: 1440
  };
  
  uni.setStorageSync('detectInfo', mockDetectInfo);
  testResult.value = '测试数据已设置，等待图片加载...';
};

const onTestImageLoad = () => {
  testResult.value = '图片加载完成，开始绘制测试...';
  
  nextTick(() => {
    setTimeout(() => {
      // 获取图片实际尺寸
      const query = uni.createSelectorQuery();
      query.select('.test-image').boundingClientRect((rect) => {
        if (rect) {
          const displayWidth = rect.width;
          const displayHeight = rect.height;
          
          // 设置canvas样式
          canvasStyle.value = {
            width: displayWidth + 'px',
            height: displayHeight + 'px'
          };
          
          showCanvas.value = true;
          
          // 计算比例并转换landmarks坐标
          const widthRatio = displayWidth / 1080;
          const heightRatio = displayHeight / 1440;
          
          const convertedLandmarks = {};
          Object.keys(mockLandmarks).forEach(part => {
            convertedLandmarks[part] = mockLandmarks[part].map(point => ({
              x: point.x * widthRatio,
              y: point.y * heightRatio
            }));
          });
          
          // 延迟绘制以确保canvas准备就绪
          setTimeout(() => {
            drawTestFacialRatios(convertedLandmarks, mockFacialRatios, displayWidth, displayHeight);
          }, 500);
        }
      }).exec();
    }, 100);
  });
};

// 简化的面部比例绘制函数（用于测试）
const drawTestFacialRatios = (landmarks, ratios, canvasWidth, canvasHeight) => {
  const ctx = uni.createCanvasContext('testCanvas');
  
  console.log('测试绘制面部比例:', ratios);
  testResult.value = '正在绘制面部比例...';
  
  // 绘制眼宽比例
  if (landmarks.left_eye && landmarks.right_eye) {
    const leftCenter = calculateCenter(landmarks.left_eye);
    const rightCenter = calculateCenter(landmarks.right_eye);
    
    drawRatioLine(ctx, [leftCenter, rightCenter], '#ff6b6b', '眼宽比例', ratios.eye_width_percent, 0);
  }
  
  // 绘制唇宽比例
  if (landmarks.outer_lips) {
    const leftPoint = landmarks.outer_lips.reduce((prev, curr) => prev.x < curr.x ? prev : curr);
    const rightPoint = landmarks.outer_lips.reduce((prev, curr) => prev.x > curr.x ? prev : curr);
    
    drawRatioLine(ctx, [leftPoint, rightPoint], '#96ceb4', '唇宽比例', ratios.lip_width_percent, 1);
  }
  
  // 绘制鼻宽比例
  if (landmarks.nose_tip) {
    const leftPoint = landmarks.nose_tip.reduce((prev, curr) => prev.x < curr.x ? prev : curr);
    const rightPoint = landmarks.nose_tip.reduce((prev, curr) => prev.x > curr.x ? prev : curr);
    
    drawRatioLine(ctx, [leftPoint, rightPoint], '#ff9ff3', '鼻宽比例', ratios.nose_width_percent, 2);
  }
  
  ctx.draw();
  testResult.value = '面部比例绘制完成！';
};

// 计算点集的中心点
const calculateCenter = (points) => {
  if (!points || points.length === 0) return { x: 0, y: 0 };
  
  const sum = points.reduce((acc, point) => ({
    x: acc.x + point.x,
    y: acc.y + point.y
  }), { x: 0, y: 0 });
  
  return {
    x: sum.x / points.length,
    y: sum.y / points.length
  };
};

// 绘制单个比例线条和标注
const drawRatioLine = (ctx, points, color, label, value, index) => {
  const [startPoint, endPoint] = points;
  
  // 绘制连接线
  ctx.beginPath();
  ctx.setLineDash([5, 5]);
  ctx.moveTo(startPoint.x, startPoint.y);
  ctx.lineTo(endPoint.x, endPoint.y);
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.stroke();
  ctx.setLineDash([]);
  
  // 绘制端点
  [startPoint, endPoint].forEach(point => {
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
  });
  
  // 计算标签位置
  const midX = (startPoint.x + endPoint.x) / 2;
  const midY = (startPoint.y + endPoint.y) / 2;
  
  const offsetX = (index % 2 === 0) ? 20 : -20;
  const offsetY = (index % 3 === 0) ? -20 : 20;
  
  const labelX = midX + offsetX;
  const labelY = midY + offsetY;
  
  // 绘制标签
  const labelText = `${label}: ${value.toFixed(2)}`;
  ctx.font = '12px Arial';
  const textWidth = ctx.measureText(labelText).width;
  const padding = 8;
  
  ctx.fillStyle = color;
  ctx.globalAlpha = 0.8;
  ctx.fillRect(labelX - padding, labelY - 16, textWidth + padding * 2, 20);
  ctx.globalAlpha = 1;
  
  ctx.fillStyle = '#fff';
  ctx.textAlign = 'left';
  ctx.fillText(labelText, labelX, labelY);
};
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.test-btn {
  background-color: #FF8F9C;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.result {
  background-color: white;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 40rpx;
  width: 100%;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.result-text {
  font-size: 24rpx;
  color: #666;
}

.image-container {
  position: relative;
  width: 100%;
  max-width: 600rpx;
}

.test-image {
  width: 100%;
  border-radius: 10rpx;
}
</style>
