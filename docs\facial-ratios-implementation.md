# 面部比例绘制功能实现文档

## 功能概述

在现有的人脸检测可视化基础上，添加了facial_ratios（面部比例）的绘制功能。该功能会在绘制各个面部部位的过程中同时显示相关的面部比例数据，而不是等到所有部位绘制完成后再绘制。

## 数据结构

### facial_ratios 数据结构
```javascript
{
  "eye_width_percent": 71.73,      // 眼宽比例
  "facial_index": 0.8,             // 面部指数
  "forehead_ratio": 1.93,          // 额头比例
  "lip_width_percent": 28.37,      // 唇宽比例
  "midface_ratio": 0.78,           // 中面部比例
  "nose_width_percent": 10.4       // 鼻宽比例
}
```

## 实现的功能

### 1. 数据处理
- 在 `processDetectInfo` 函数中添加了对 `facial_ratios` 数据的处理
- 将面部比例数据存储到响应式变量 `facialRatios` 中

### 2. 部位与比例关联
定义了面部部位与比例的关联关系：
- `left_eye/right_eye` → `eye_width_percent` (眼宽比例)
- `left_eyebrow/right_eyebrow` → `forehead_ratio` (额头比例)
- `nose_bridge` → `midface_ratio` (中面部比例)
- `nose_tip` → `nose_width_percent` (鼻宽比例)
- `outer_lips` → `lip_width_percent`, `midface_ratio` (唇宽比例、中面部比例)
- `jaw` → `facial_index` (面部指数)

### 3. 动画绘制
- 在每个面部部位绘制完成后，立即绘制相关的面部比例
- 使用动画效果绘制比例线条和标注
- 比例线条使用虚线样式，端点有发光效果
- 标签包含比例名称和数值，带有背景和边框

### 4. 视觉效果
- **连接线**: 使用虚线样式，线宽3px，颜色根据比例类型区分
- **端点**: 圆形标记，半径6px，带有发光效果
- **标签**: 包含比例名称和数值，带有彩色背景和白色边框
- **指示线**: 从标签指向比例线条中点的虚线

## 核心函数

### 1. `drawRelatedFacialRatiosWithAnimation`
根据当前绘制的面部部位，绘制相关的面部比例，带有动画效果。

### 2. `drawSpecificRatioWithAnimation`
绘制特定的面部比例，包括计算关键点位置和调用动画绘制函数。

### 3. `drawRatioLineWithAnimation`
带动画效果的比例线绘制函数，实现线条从起点到终点的动画效果。

### 4. `calculateCenter`
计算点集的中心点，用于确定眼部、唇部等区域的中心位置。

## 绘制时机和时序控制

### 时序优化
为了确保用户有足够时间观看每个面部比例，我们优化了绘制时序：

1. **部位绘制完成后延迟**: 500ms后开始绘制相关面部比例
2. **比例间隔时间**: 每个比例间隔1200ms绘制，避免视觉混乱
3. **单个比例动画时间**: 2秒完成线条和标签的绘制动画
4. **动画完成后延迟**: 800ms让用户观看完整效果
5. **所有比例完成后延迟**: 1秒后才切换到下一个面部部位
6. **部位切换延迟**: 800ms后开始绘制下一个部位

### 绘制流程
1. **实时绘制**: 在每个面部部位绘制完成后，立即绘制相关的面部比例
2. **回调控制**: 使用回调函数确保所有比例绘制完成后才切换到下一个部位
3. **最终绘制**: 在所有部位绘制完成后，再次绘制所有面部比例确保完整显示

### 总时间估算
- 单个比例绘制: ~3.8秒 (动画2秒 + 延迟1.8秒)
- 多个比例部位: 第一个比例3.8秒 + 后续比例每个1.2秒间隔
- 例如眼部(1个比例): ~4.3秒
- 例如嘴唇(2个比例): ~5.5秒

## 使用方法

1. 确保 `detectInfo.detect.facial_ratios` 包含有效的面部比例数据
2. 系统会自动在人脸检测可视化过程中绘制相关比例
3. 比例数据会以动画形式逐步显示，增强用户体验

## 调试信息

代码中添加了详细的控制台输出，便于调试：
- 面部比例数据的获取和处理
- 每个部位相关比例的绘制过程
- 动画绘制的进度和状态

## 注意事项

1. 面部比例的绘制依赖于landmarks数据的准确性
2. 标签位置会根据线条方向和索引自动调整，避免重叠
3. 所有绘制都在canvas上进行，不会影响原始图片
4. 动画效果使用requestAnimationFrame实现，性能优化良好
