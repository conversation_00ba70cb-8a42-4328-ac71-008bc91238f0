import {
	defineConfig
} from 'vite';
import uni from '@dcloudio/vite-plugin-uni';

export default defineConfig({
	plugins: [uni()],
	server: {
		proxy: {
			// 代理规则示例：将所有以 /api 开头的请求代理到目标服务器
			'/api': {
				target: 'http://192.168.0.124:9209', // 目标服务器地址
				rewrite: (path) => path.replace(/^\/api/, ''),
				// pathRewrite: { //匹配请求路径里面有 /api 会替换成https://www.test.com
				// 	// 举例：/api/api/user => https://www.test.com/api/user
				// 	"/api": ""
				// },
			},
			'/oss': {
				target: 'http://8.130.87.164:8083', // 目标服务器地址
				rewrite: (path) => path.replace(/^\/oss/, ''),
				// pathRewrite: { //匹配请求路径里面有 /api 会替换成https://www.test.com
				// 	// 举例：/api/api/user => https://www.test.com/api/user
				// 	"/api": ""
				// },
			},
			
			
		}
	}
});