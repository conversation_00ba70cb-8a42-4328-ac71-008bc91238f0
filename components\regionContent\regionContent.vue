<template>
	<view class="analysis-modal" v-if="visible" @click="handleMaskClick">
		<view class="modal-content" @click.stop>
			<!-- 左上角标题 -->
			<view class="title">{{ title.replace('图', '分析') }}</view>
			<view class="fraction" v-if="title=='皱纹图'">得分：{{ progressData.score_info.wrinkle_score }}</view>
			<view class="fraction" v-if="title=='油光图'">得分：{{ progressData.score_info.oily_intensity_score }}</view>
			<view class="fraction" v-if="title=='毛孔图'">得分：{{ progressData.score_info.pores_score }}</view>
			<view class="fraction" v-if="title=='黑头图'">得分：{{ progressData.score_info.blackhead_score }}</view>
			<view class="fraction" v-if="title=='痤疮图'">得分：{{ progressData.score_info.acne_score }}</view>
			<view class="fraction" v-if="title=='水分图'">得分：{{ progressData.score_info.water_score }}</view>
			<view class="fraction" v-if="title=='红区图'">得分：{{ progressData.score_info.sensitivity_score }}</view>
			<view class="fraction" v-if="title=='色沉图'">得分：{{ progressData.score_info.melanin_score }}</view>
			
			<!-- 进度条列表 -->
			<view class="progress-list" v-if="title=='油光图'">
				<!-- 油脂 -->
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">T区油脂</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.oily_intensity.t_zone.area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.oily_intensity.t_zone.area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">下巴油脂</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.oily_intensity.chin_area.area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.oily_intensity.chin_area.area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.oily_intensity.chin_area.intensity!=undefined">{{ progressData.oily_intensity.chin_area.intensity==0?'轻微':progressData.oily_intensity.chin_area.intensity==1?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左脸油脂</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.oily_intensity.left_cheek.area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.oily_intensity.left_cheek.area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.oily_intensity.left_cheek.intensity!=undefined">{{ progressData.oily_intensity.left_cheek.intensity==0?'轻微':progressData.oily_intensity.left_cheek.intensity==1?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">右脸油脂</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.oily_intensity.right_cheek.area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.oily_intensity.right_cheek.area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.oily_intensity.right_cheek.intensity!=undefined">{{ progressData.oily_intensity.right_cheek.intensity==0?'轻微':progressData.oily_intensity.right_cheek.intensity==1?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">全脸油脂</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.oily_intensity.full_face.area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.oily_intensity.full_face.area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.oily_intensity.full_face.intensity!=undefined">{{ progressData.oily_intensity.full_face.intensity==0?'轻微':progressData.oily_intensity.full_face.intensity==1?'中度':'严重' }}</view>
				</view>
			</view>
			<view class="progress-list" v-if="title=='水分图'">
				
				<view class="" v-if="false">
					<text class="item-label">水分分数   {{ 100- progressData.water.water_severity}}</text>
					<text class="item-label"></text>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">额头水分</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.water.water_forehead.area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.water.water_forehead.area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.water.water_forehead.intensity!=undefined">{{ progressData.water.water_forehead.intensity==0?'轻微':progressData.water.water_forehead.intensity==1?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左脸水分</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.water.water_leftcheek.area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.water.water_leftcheek.area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.water.water_leftcheek.intensity!=undefined">{{ progressData.water.water_leftcheek.intensity==0?'轻微':progressData.water.water_leftcheek.intensity==1?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左脸水分</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.water.water_rightcheek.area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.water.water_rightcheek.area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.water.water_rightcheek.intensity!=undefined">{{ progressData.water.water_rightcheek.intensity==0?'轻微':progressData.water.water_rightcheek.intensity==1?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">整体水分</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.water.water_area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.water.water_area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.water.water_rightcheek.intensity!=undefined">{{ progressData.water.water_rightcheek.intensity==0?'轻微':progressData.water.water_rightcheek.intensity==1?'中度':'严重' }}</view>
				</view>
			</view>
			<view class="progress-list" v-if="title=='毛孔图'">
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">额头毛孔数</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<!-- <view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.water.water_forehead.area*100 ).toFixed(0) + '%' }"></view>
						</view> -->
						<view class="percentage-text">{{ (progressData.enlarged_pore_count.forehead_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.pores_forehead.confidence!=undefined">{{ progressData.pores_forehead.confidence==1?'轻微':progressData.pores_forehead.confidence==2?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左脸毛孔数</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<!-- <view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.water.water_forehead.area*100 ).toFixed(0) + '%' }"></view>
						</view> -->
						<view class="percentage-text">{{ (progressData.enlarged_pore_count.left_cheek_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.pores_left_cheek.confidence!=undefined">{{ progressData.pores_left_cheek.confidence==1?'轻微':progressData.pores_left_cheek.confidence==2?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">右脸毛孔数</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<!-- <view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.water.water_forehead.area*100 ).toFixed(0) + '%' }"></view>
						</view> -->
						<view class="percentage-text">{{ (progressData.enlarged_pore_count.right_cheek_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.pores_right_cheek.confidence!=undefined">{{ progressData.pores_right_cheek.confidence==1?'轻微':progressData.pores_right_cheek.confidence==2?'中度':'严重' }}</view>
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">下巴毛孔数</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<!-- <view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.water.water_forehead.area*100 ).toFixed(0) + '%' }"></view>
						</view> -->
						<view class="percentage-text">{{ (progressData.enlarged_pore_count.chin_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.pores_jaw.confidence!=undefined">{{ progressData.pores_jaw.confidence==1?'轻微':progressData.pores_jaw.confidence==2?'中度':'严重' }}</view>
				</view>
			</view>
			<view class="progress-list" v-if="title=='色沉图'">
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">额头色沉</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.melanin.brown_forehead*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.melanin.brown_forehead*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左脸色沉</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.melanin.brown_leftcheek*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.melanin.brown_leftcheek*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">右脸色沉</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.melanin.brown_rightcheek*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.melanin.brown_rightcheek*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">全脸色沉</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.melanin.brown_area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.melanin.brown_area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
			</view>
			<view class="progress-list" v-if="title=='黑头图'">
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">黑头个数</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text">{{ progressData.blackhead_count }}</view>
						
					</view>
					<!-- 右侧状态文字 -->
					<view class="status-text" v-if="progressData.blackhead.confidence!=undefined">{{ progressData.blackhead.confidence==0?'无黑头':progressData.blackhead.confidence==1?'轻度':progressData.blackhead.confidence==2?'中度':'严重' }}</view>
				</view>
			</view>
			<view class="progress-list" v-if="title=='皱纹图'">
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">抬头纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">细纹和深纹</view>
						<view class="percentage-text">{{ (progressData.fine_line.forehead_count)}},{{ (progressData.wrinkle_count.forehead_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左眼皱纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">细纹和深纹</view>
						<view class="percentage-text">{{ (progressData.fine_line.left_undereye_count)}},{{ (progressData.wrinkle_count.left_undereye_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">右眼皱纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">细纹和深纹</view>
						<view class="percentage-text">{{ (progressData.fine_line.right_undereye_count)}},{{ (progressData.wrinkle_count.right_undereye_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左眼鱼尾纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">细纹和深纹</view>
						<view class="percentage-text">{{ (progressData.fine_line.left_crowsfeet_count)}},{{ (progressData.wrinkle_count.left_crowsfeet_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左眼鱼尾纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">细纹和深纹</view>
						<view class="percentage-text">{{ (progressData.fine_line.right_crowsfeet_count)}},{{ (progressData.wrinkle_count.right_crowsfeet_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">眉间纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">细纹和深纹</view>
						<view class="percentage-text">{{ (progressData.fine_line.glabella_count)}},{{ (progressData.wrinkle_count.glabella_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">左脸皱纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">细纹和深纹</view>
						<view class="percentage-text">{{ (progressData.fine_line.left_cheek_count)}},{{ (progressData.wrinkle_count.left_cheek_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">右脸皱纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">细纹和深纹</view>
						<view class="percentage-text">{{ (progressData.fine_line.right_cheek_count)}},{{ (progressData.wrinkle_count.right_cheek_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">法令纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">左法令纹和右法令纹</view>
						<view class="percentage-text">{{ (progressData.wrinkle_count.left_nasolabial_count)}},{{ (progressData.wrinkle_count.right_nasolabial_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">嘴角纹：</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text" style="flex: 1;text-align: center;">左嘴角纹和右嘴角纹</view>
						<view class="percentage-text">{{ (progressData.wrinkle_count.left_mouth_count)}},{{ (progressData.wrinkle_count.right_nasolabial_count)}}</view>
					</view>
					<!-- 右侧状态文字 -->
				</view>
			</view>
			<view class="progress-list" v-if="title=='红区图'">
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">敏感程度</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.sensitivity.sensitivity_intensity ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.sensitivity.sensitivity_intensity).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">全脸占比</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="progress-bg">
							<view class="progress-fill" :style="{ width: (progressData.sensitivity.sensitivity_area*100 ).toFixed(0) + '%' }"></view>
						</view>
						<view class="percentage-text">{{ (progressData.sensitivity.sensitivity_area*100).toFixed(0) }}%</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
			</view>
			<view class="progress-list" v-if="title=='色斑图'">
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">黄褐斑</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text">{{ (progressData.melasma.value)==0?'无':"有" }}</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">雀斑</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text">{{ (progressData.freckle.value)==0?'无':"有" }}</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
			</view>
			<view class="progress-list" v-if="title=='闭口粉刺图'">
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">个数</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text">{{ (progressData.closed_comedones.polygon.filter(item=>item.x).length) }}</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
			</view>
			<view class="progress-list" v-if="title=='痣图'">
				<view  class="progress-item" >
					<!-- 左侧文字 -->
					<view class="item-label">个数</view>
					<!-- 中间进度条 -->
					<view class="progress-bar">
						<view class="percentage-text">{{ (progressData.mole.polygon.filter(item=>item.x).length) }}</view>
					</view>
					<!-- 右侧状态文字 -->
					<!-- <view class="status-text" v-if="progressData.oily_intensity.t_zone.intensity!=undefined">{{ progressData.oily_intensity.t_zone.intensity==0?'轻微':progressData.oily_intensity.t_zone.intensity==1?'中度':'严重' }}</view> -->
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { onMounted, toRefs, watch,ref } from 'vue'

// 定义组件属性
const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	title: {
		type: String,
		default: '油脂分析'
	},
	progressData: {
		type: Object,
		default: () => {}
	}
})
let { progressData, title, visible } = toRefs(props)
let text = {
	chin_area:"下巴油脂",
	full_face:"全脸油脂",
	left_cheek:"左脸油脂",
	right_cheek:"右脸油脂",
	t_zone:"T区油脂",
	water_forehead:"额头缺水",
	water_leftcheek:"左脸缺水",
	water_rightcheek:"右脸缺水",


}
let list = ref([])
watch(progressData, (newval) => {
	
	return false
	let obj = {}
	obj = JSON.parse(JSON.stringify(newval))
	for (const key in obj) {
		// 检查是否是对象自身的属性，而非继承的
		if(typeof obj[key]=='object'){
			obj[key].text = text[key];
		}else{
			delete obj[key]
		}
	}
	list.value = Object.values(obj)
	console.log(list.value);
	

})
onMounted(() => {
	// console.log(progressData);

})
// 定义事件
const emit = defineEmits(['close'])

// 处理遮罩点击
const handleMaskClick = () => {
	emit('close')
}
</script>

<style lang="scss">
.item-label {
					color: white;
					font-size: 28rpx;
					width: 140rpx;
					flex-shrink: 0;
					text-align: right;
				}
.analysis-modal {
	position: fixed;
	bottom: 200rpx;
	left: 50%;
	transform: translateX(-50%);
	// background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 999;

	.modal-content {
		width: 600rpx;
		overflow: hidden;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		padding: 40rpx;
		position: relative;
		// backdrop-filter: blur(10rpx);
		background: rgba(31, 31, 31, 0.53);
		border: 1px solid #fff;
		box-shadow: 0 0 48px -16px #ff6d65;

		.title {
			position: absolute;
			top: 0rpx;
			left: 0rpx;
			background: #FA7777;
			color: white;
			padding: 8rpx 20rpx;
			border-bottom-right-radius: 20rpx;
			font-size: 24rpx;
			font-weight: bold;
		}
		.fraction{
			position: absolute;
			top: 0rpx;
			right: 0rpx;
			background: #FA7777;
			color: white;
			padding: 8rpx 20rpx;
			border-bottom-left-radius: 20rpx;
			font-size: 24rpx;
			font-weight: bold;
		}
		.progress-list {
			margin-top: 30rpx;

			.progress-item {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.item-label {
					color: white;
					font-size: 28rpx;
					width: 180rpx;
					flex-shrink: 0;
					text-align: right;
				}

				.progress-bar {
					flex: 1;
					display: flex;
					align-items: center;
					margin: 0 20rpx;

					.progress-bg {
						flex: 1;
						height: 12rpx;
						background-color: rgba(255, 255, 255, 0.3);
						border-radius: 6rpx;
						overflow: hidden;
						margin-right: 15rpx;

						.progress-fill {
							height: 100%;
							background: linear-gradient(90deg, #FF6B6B 0%, #FF4757 100%);
							border-radius: 6rpx;
							transition: width 0.3s ease;
						}
					}

					.percentage-text {
						color: white;
						font-size: 24rpx;
						width: 60rpx;
						text-align: right;
					}
				}

				.status-text {
					background-color: rgba(255, 255, 255, 0.6);
					color: #666;
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 22rpx;
					width: 80rpx;
					text-align: center;
					flex-shrink: 0;
				}
			}
		}
	}
}
</style>