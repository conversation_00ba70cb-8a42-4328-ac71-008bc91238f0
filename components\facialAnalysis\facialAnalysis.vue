<template>
	<view style="transition: all 0.3s;justify-content: center;position: relative;" class="w-full flex-1 flex flex-col">
		<image ref="beforeimgRef" :src="props.peopleImg" mode="widthFix" style="width: 100%;" />
	</view>
</template>

<script setup>
import { callAiPolling } from "@/Api/index.js"
import { ref, onMounted, onBeforeUnmount } from "vue"
const emit = defineEmits(['update:loading', 'update:percent']);
const props = defineProps({
	peopleImg: {
		type: String,
		default: ''
	}
});


let timer = null

onMounted(() => {



	emit('update:loading', true)
	let operationId = uni.getStorageSync('operationId')
	if (operationId) {
		getStatus(operationId)

		timer = setInterval(async () => {
			getStatus(operationId)
		}, 3000)
	}
})
onBeforeUnmount(() => {

	clearInterval(timer)
})
async function getStatus(operationId) {
	let { data } = await callAiPolling({ operationId, type: 'face' })
	const completedCount = data.data.filter(item => item.aiStatus == '1').length
	const totalCount = data.data.length
	const percent = Math.round((completedCount / totalCount) * 100)
	// 更新进度
	emit('update:percent', percent)

	let status = data.data.every(item => item.aiStatus == '1')
	if (status) {
		clearInterval(timer)
		// 完成后隐藏loading
		emit('update:loading', false)
		emit('update:percent', 100)
		organizeData(data.data)

	}
}
let face = ref({})
function organizeData(data) {

	face.value = JSON.parse(data.filter(item => item.aiType == "face")[0].aiResult)
	console.log(face.value);

}
</script>

<style lang="scss"></style>