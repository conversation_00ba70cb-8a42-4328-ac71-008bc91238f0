<template>
	<view class="example-page">
		<view class="header">
			<text class="title">使用示例页面</text>
		</view>
		
		<view class="content">
			<button class="open-btn" @click="openAnalysisModal">打开油脂分析弹窗</button>
			<button class="open-btn" @click="openCustomModal">打开自定义数据弹窗</button>
		</view>
		
		<!-- 引入油脂分析组件 -->
		<region-content 
			:visible="showModal"
			:title="modalTitle"
			:progress-data="progressData"
			@close="closeModal"
		/>
	</view>
</template>

<script setup>
	import { ref } from 'vue'
	import RegionContent from '@/components/regionContent/regionContent.vue'
	
	// 响应式数据
	const showModal = ref(false)
	const modalTitle = ref('油脂分析')
	const progressData = ref([
		{ label: 'T区油脂', percentage: 15, status: '轻微' },
		{ label: '左脸颊油脂', percentage: 2, status: '无' },
		{ label: '右脸颊油脂', percentage: 2, status: '无' }
	])
	
	// 打开默认油脂分析弹窗
	const openAnalysisModal = () => {
		modalTitle.value = '油脂分析'
		progressData.value = [
			{ label: 'T区油脂', percentage: 15, status: '轻微' },
			{ label: '左脸颊油脂', percentage: 2, status: '无' },
			{ label: '右脸颊油脂', percentage: 2, status: '无' }
		]
		showModal.value = true
	}
	
	// 打开自定义数据弹窗
	const openCustomModal = () => {
		modalTitle.value = '水分分析'
		progressData.value = [
			{ label: 'T区水分', percentage: 45, status: '正常' },
			{ label: '左脸颊水分', percentage: 38, status: '偏低' },
			{ label: '右脸颊水分', percentage: 42, status: '正常' },
			{ label: '下巴水分', percentage: 35, status: '偏低' }
		]
		showModal.value = true
	}
	
	// 关闭弹窗
	const closeModal = () => {
		showModal.value = false
	}
</script>

<style lang="scss">
.example-page {
	padding: 40rpx;
	
	.header {
		text-align: center;
		margin-bottom: 60rpx;
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 30rpx;
		
		.open-btn {
			width: 400rpx;
			height: 80rpx;
			background: linear-gradient(90deg, #FF6B6B 0%, #FF8E8E 100%);
			color: white;
			border: none;
			border-radius: 40rpx;
			font-size: 28rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
</style>
